<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.tuns.admin.tenancy</groupId>
        <artifactId>admin-tenancy</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>admin-tenancy-biz</artifactId>
    <packaging>jar</packaging>

    <version>${package.environment}-SNAPSHOT</version>
    <name>admin-tenancy-biz</name>

    <description>业务逻辑模块</description>

    <dependencies>
        <dependency>
            <groupId>com.tuns.admin.tenancy</groupId>
            <artifactId>admin-tenancy-api</artifactId>
            <version>${package.environment}-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.tuns.framework</groupId>
            <artifactId>tuns-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tuns.framework</groupId>
            <artifactId>tuns-starter-nacos</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tuns.framework</groupId>
            <artifactId>tuns-starter-mybatis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tuns.framework</groupId>
            <artifactId>tuns-starter-rpc</artifactId>
        </dependency>
        <dependency>
            <groupId>tuns.biz.component</groupId>
            <artifactId>tuns-component-log</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.tuns.admin.iam</groupId>
            <artifactId>admin-iam-api</artifactId>
            <version>${package.environment}-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.tuns.company.insurer</groupId>
            <artifactId>company-insurer-api</artifactId>
            <version>${package.environment}-SNAPSHOT</version>
        </dependency>
    </dependencies>
</project>
