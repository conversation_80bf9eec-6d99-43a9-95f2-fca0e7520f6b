<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tuns.admin.tenancy.biz.dao.mapper.ProductMapper">

    <select id="listAllAreSort" resultType="com.tuns.admin.tenancy.biz.dao.entity.Product">
        SELECT product_code, product_name, terminal_type, status, use_type, main_flag
        FROM product
        where delete_flag = 0
        ORDER BY FIELD(status, 1, 0, 2), sort_no
    </select>
</mapper>
