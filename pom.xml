<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.tuns.parent</groupId>
    <artifactId>tuns-starter-parent</artifactId>
    <version>1.0.0-SNAPSHOT</version>
  </parent>

  <groupId>com.tuns.central.gateway</groupId>
  <artifactId>tuns-central-gateway</artifactId>
  <version>1.0.0-SNAPSHOT</version>
  <packaging>jar</packaging>

  <name>tuns-central-gateway</name>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>

  <dependencies>
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-gateway</artifactId>
      <exclusions>
        <exclusion>
          <groupId>org.springframework.cloud</groupId>
          <artifactId>spring-cloud-sleuth-zipkin</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.tuns.framework</groupId>
      <artifactId>tuns-starter-redis</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-loadbalancer</artifactId>
    </dependency>
    <dependency>
      <groupId>com.tuns.framework</groupId>
      <artifactId>tuns-starter-nacos</artifactId>
    </dependency>
    <dependency>
      <groupId>com.tuns.framework</groupId>
      <artifactId>tuns-starter-auth</artifactId>
    </dependency>
    <dependency>
      <groupId>cn.dev33</groupId>
      <artifactId>sa-token-reactor-spring-boot3-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>com.tuns.framework</groupId>
      <artifactId>tuns-starter-log</artifactId>
    </dependency>
    <dependency>
      <groupId>com.tuns.framework</groupId>
      <artifactId>tuns-starter-rpc</artifactId>
    </dependency>
    <dependency>
      <groupId>com.tuns.admin.iam</groupId>
      <artifactId>admin-iam-api</artifactId>
      <version>${package.environment}-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.tuns.cloud.iam</groupId>
      <artifactId>cloud-iam-api</artifactId>
      <version>${package.environment}-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.tuns.cloud.crm</groupId>
      <artifactId>cloud-crm-api</artifactId>
      <version>${package.environment}-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>cn.dev33</groupId>
      <artifactId>sa-token-redis-jackson</artifactId>
    </dependency>
<!--    <dependency>-->
<!--      <groupId>cn.dev33</groupId>-->
<!--      <artifactId>sa-token-jwt</artifactId>-->
<!--    </dependency>-->
<!--    <dependency>-->
<!--      <groupId>cn.dev33</groupId>-->
<!--      <artifactId>sa-token-redis-template</artifactId>-->
<!--    </dependency>-->
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-pool2</artifactId>
    </dependency>
    <dependency>
      <groupId>com.github.xiaoymin</groupId>
      <artifactId>knife4j-gateway-spring-boot-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>cn.hutool</groupId>
      <artifactId>hutool-all</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>io.projectreactor</groupId>
      <artifactId>reactor-test</artifactId>
      <version>3.6.4</version> <!-- 建议使用和你 Spring Boot 所依赖的 Reactor 版本一致 -->
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <configuration>
          <executable>false</executable>
          <outputDirectory>${project.build.directory}</outputDirectory>
          <finalName>${project.name}</finalName>
        </configuration>
        <executions>
          <execution>
            <goals>
              <goal>repackage</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>
