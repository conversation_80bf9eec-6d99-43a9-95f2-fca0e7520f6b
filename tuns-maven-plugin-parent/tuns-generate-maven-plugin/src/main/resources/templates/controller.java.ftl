package ${package.Controller};

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tuns.framework.common.result.Result;
import com.tuns.framework.mybatis.result.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
<#if restControllerStyle>
import org.springframework.web.bind.annotation.*;
<#else>
import org.springframework.stereotype.Controller;
</#if>
<#if superControllerClassPackage??>
import ${superControllerClassPackage};
</#if>

/**
 * <p>
 * ${table.comment!} 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since ${date}
 */
@Tag(name = "${table.comment}")
<#if restControllerStyle>
@RestController
<#else>
@Controller
</#if>
<#--@RequestMapping("<#if package.ModuleName?? && package.ModuleName != "">/${package.ModuleName}</#if>/<#if controllerMappingHyphenStyle>${controllerMappingHyphen}<#else>${table.entityPath}</#if>")-->
@RequestMapping("/${controllerMappingHyphen?replace('-', '/')?replace('/ref$', '', 'r')}")
<#if kotlin>
class ${table.controllerName}<#if superControllerClass??> : ${superControllerClass}()</#if>
<#else>
<#if superControllerClass??>
public class ${table.controllerName} extends ${superControllerClass} {
<#else>
public class ${table.controllerName} {
</#if>

    @Operation(summary = "分页查询")
    // @TunsScanPermission("XXX:XXX:page")
    @PostMapping("/page")
    public PageResult<Object> page(@RequestBody Object param) {
    // public PageResult<${entity}QueryVO> page(@RequestBody ${entity}QueryParam param) {
        // Page<${entity}QueryVO> page = XXX.page(param);
        return PageResult.success(new Page<>());
    }

    @Operation(summary = "新增")
    // @TunsScanPermission("XXX:XXX:save")
    // @RequestLogIntercept(code = "xxx_xxx_save", logType = LogTypeEnum.)
    @PostMapping
    public Result<Boolean> save(@Valid @RequestBody Object param) {
    // public Result<Boolean> save(@Valid @RequestBody ${entity}SaveParam param) {
        // XXX.save(param);
        return Result.success();
    }

    @Operation(summary = "删除")
    // @TunsScanPermission("XXX:XXX:delete")
    // @RequestLogIntercept(code = "XXX_XXX_delete", logType = LogTypeEnum.)
    @DeleteMapping
    public Result<Boolean> delete(@RequestParam Long id) {
        // XXX.delete(id);
        return Result.success();
    }

    @Operation(summary = "修改")
    // @TunsScanPermission("XXX:XXX:update")
    // @RequestLogIntercept(code = "XXX_XXX_update", logType = LogTypeEnum.)
    @PutMapping
    public Result<Boolean> update(@Valid @RequestBody Object param) {
    // public Result<Boolean> update(@Valid @RequestBody ${entity}UpdateParam param) {
        // XXX.update(param);
        return Result.success();
    }

     @Operation(summary = "详情")
     // @TunsScanPermission("XXX:XXX:detail")
     @GetMapping
     public Result<Object> detail(@RequestParam Long id) {
     // public Result<${entity}DetailVO> detail(@RequestParam Long id) {
          // return Result.success(XXX.detail(id));
          return Result.success();
     }

}
</#if>
