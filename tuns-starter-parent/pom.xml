<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.tuns.parent</groupId>
    <artifactId>tuns-starter-parent</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <name>tuns-starter-parent</name>
    <description>统一父工程，定义全局属性、插件、构建配置</description>

    <modules>
        <module>../tuns-framework</module>
    </modules>
    <properties>
        <revision>1.0.0-SNAPSHOT</revision>
        <java.version>17</java.version>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <maven.compiler.plugin>3.13.0</maven.compiler.plugin>
        <flatten-maven-plugin.version>1.6.0</flatten-maven-plugin.version>
        <tuns-generate-maven-plugin.version>1.0.0-SNAPSHOT</tuns-generate-maven-plugin.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven-surefire-plugin.version>3.2.3</maven-surefire-plugin.version>
        <jacoco-maven-plugin.version>0.8.13</jacoco-maven-plugin.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.tuns.dependencies</groupId>
                <artifactId>tuns-dependencies</artifactId>
                <version>2025.1.0-SNAPSHOT</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.tuns.framework</groupId>
                <artifactId>tuns-common-core</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.tuns.framework</groupId>
                <artifactId>tuns-starter-web</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.tuns.framework</groupId>
                <artifactId>tuns-starter-mybatis</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.tuns.framework</groupId>
                <artifactId>tuns-starter-datasource</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.tuns.framework</groupId>
                <artifactId>tuns-starter-excel</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.tuns.framework</groupId>
                <artifactId>tuns-starter-perception</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.tuns.framework</groupId>
                <artifactId>tuns-starter-protection</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.tuns.framework</groupId>
                <artifactId>tuns-starter-redis</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.tuns.framework</groupId>
                <artifactId>tuns-starter-sms</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.tuns.framework</groupId>
                <artifactId>tuns-starter-rpc</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.tuns.framework</groupId>
                <artifactId>tuns-starter-nacos</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.tuns.framework</groupId>
                <artifactId>tuns-starter-file</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.tuns.framework</groupId>
                <artifactId>tuns-starter-doc</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.tuns.framework</groupId>
                <artifactId>tuns-starter-log</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.tuns.framework</groupId>
                <artifactId>tuns-starter-integration</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.tuns.framework</groupId>
                <artifactId>tuns-starter-auth</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.tuns.framework</groupId>
                <artifactId>tuns-starter-job</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.tuns.framework</groupId>
                <artifactId>tuns-starter-tx</artifactId>
                <version>${revision}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>local</id>
            <properties>
                <package.environment>local</package.environment>
                <nacos.server>127.0.0.1:8848</nacos.server>
                <nacos.discovery.group>DEFAULT_GROUP</nacos.discovery.group>
                <nacos.config.group>DEFAULT_GROUP</nacos.config.group>
                <nacos.dubbo.namespace>DUBBO</nacos.dubbo.namespace>
                <nacos.dubbo.group>DUBBO_GROUP</nacos.dubbo.group>
                <nacos.username>nacos</nacos.username>
                <nacos.password>nacos</nacos.password>
            </properties>
        </profile>
        <profile>
            <id>dev</id>
<!--            <activation>-->
<!--                <activeByDefault>true</activeByDefault>-->
<!--            </activation>-->
            <properties>
                <package.environment>dev</package.environment>
                <nacos.server>192.168.2.31:8848</nacos.server>
                <nacos.discovery.group>DEFAULT_GROUP</nacos.discovery.group>
                <nacos.config.group>DEFAULT_GROUP</nacos.config.group>
                <nacos.dubbo.namespace>DUBBO</nacos.dubbo.namespace>
                <nacos.dubbo.group>DUBBO_GROUP</nacos.dubbo.group>
                <nacos.username>nacos</nacos.username>
                <nacos.password>nacos</nacos.password>
            </properties>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <package.environment>test</package.environment>
                <nacos.server>192.168.2.30:8848</nacos.server>
                <nacos.discovery.group>DEFAULT_GROUP</nacos.discovery.group>
                <nacos.config.group>DEFAULT_GROUP</nacos.config.group>
                <nacos.dubbo.namespace>DUBBO</nacos.dubbo.namespace>
                <nacos.dubbo.group>DUBBO_GROUP</nacos.dubbo.group>
                <nacos.username>nacos</nacos.username>
                <nacos.password>nacos</nacos.password>
            </properties>
        </profile>
        <profile>
            <id>pre</id>
            <properties>
                <package.environment>pre</package.environment>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <package.environment>prod</package.environment>
            </properties>
        </profile>
    </profiles>

    <build>
        <resources>
            <!-- 启用过滤的文件 -->
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>**/bootstrap*.yml</include>
                    <include>**/application*.yml</include>
                    <include>**/doc.html</include>
                </includes>
            </resource>

            <!-- 不启用过滤的其他文件 -->
            <resource>
                <directory>src/main/resources</directory>
                <filtering>false</filtering>
                <excludes>
                    <exclude>**/bootstrap*.yml</exclude>
                    <exclude>**/application*.yml</exclude>
                    <exclude>**/doc.html</exclude>
                </excludes>
            </resource>
        </resources>
        <pluginManagement>
            <plugins>
                <!-- 统一测试插件配置 -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>${maven-surefire-plugin.version}</version>
                    <configuration>
                        <useModulePath>false</useModulePath>
                        <includes>
                            <include>**/*Test.java</include>
                            <include>**/*Tests.java</include>
                        </includes>
                        <excludes>
                            <exclude>**/Abstract*.java</exclude>
                        </excludes>
                        <argLine>-Dfile.encoding=UTF-8</argLine>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.jacoco</groupId>
                    <artifactId>jacoco-maven-plugin</artifactId>
                    <version>${jacoco-maven-plugin.version}</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>prepare-agent</goal>
                            </goals>
                        </execution>
                        <execution>
                            <id>report</id>
                            <phase>test</phase>
                            <goals>
                                <goal>report</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven.compiler.plugin}</version>
                <configuration>
                    <release>${java.version}</release>
                    <!-- 兼容性配置 -->
                    <compilerArgs>
                        <arg>-Xlint:all</arg>
                    </compilerArgs>
                    <showWarnings>true</showWarnings>
                    <showDeprecation>true</showDeprecation>
                    <parameters>true</parameters>
                </configuration>
            </plugin>
            <!-- 统一 revision 版本 -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>${flatten-maven-plugin.version}</version>
                <inherited>true</inherited>
                <configuration>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                    <pomElements>
                        <version>resolve</version>
                    </pomElements>
                </configuration>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.tuns.maven.plugin</groupId>
                <artifactId>tuns-generate-maven-plugin</artifactId>
                <version>${tuns-generate-maven-plugin.version}</version>
            </plugin>
            <!-- 配置 maven-source-plugin 以生成源代码 JAR -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.3.0</version> <!-- 请使用最新稳定版本 -->
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar-no-fork</goal> <!-- 使用 no-fork 避免额外的构建周期 -->
                        </goals>
                        <phase>package</phase> <!-- 在 package 阶段生成源代码 JAR -->
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
